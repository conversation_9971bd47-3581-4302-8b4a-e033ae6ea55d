<?php
// Test script to verify appointment and order cancel/edit functionality
require_once 'includes/data.php';

echo "<h2>🔧 Cancel & Edit Functionality Test</h2>";

// Check if database connection is working
if ($conn->connect_error) {
    die("<p style='color: red;'>Connection failed: " . $conn->connect_error . "</p>");
}

echo "<p style='color: green;'>✓ Database connection successful</p>";

// Check if required tables and columns exist
echo "<h3>📋 Database Structure Check:</h3>";

// Check appointments table structure
echo "<h4>Appointments Table:</h4>";
$appointments_check = $conn->query("DESCRIBE appointments");
if ($appointments_check) {
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    while ($row = $appointments_check->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['Field'] . "</td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "<td>" . $row['Default'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Check for required columns
    $required_appointment_columns = ['appointment_id', 'status', 'cancellation_reason', 'updated_at'];
    $appointment_columns = [];
    $appointments_check->data_seek(0);
    while ($row = $appointments_check->fetch_assoc()) {
        $appointment_columns[] = $row['Field'];
    }
    
    $missing_appointment_cols = array_diff($required_appointment_columns, $appointment_columns);
    if (empty($missing_appointment_cols)) {
        echo "<p style='color: green;'>✓ All required appointment columns present</p>";
    } else {
        echo "<p style='color: orange;'>⚠ Missing appointment columns: " . implode(', ', $missing_appointment_cols) . "</p>";
        echo "<p>Run the add_cancel_edit_functionality.sql script to add missing columns.</p>";
    }
} else {
    echo "<p style='color: red;'>✗ Appointments table not found</p>";
}

// Check orders table structure
echo "<h4>Orders Table:</h4>";
$orders_check = $conn->query("DESCRIBE orders");
if ($orders_check) {
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    while ($row = $orders_check->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['Field'] . "</td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "<td>" . $row['Default'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Check for required columns
    $required_order_columns = ['order_id', 'order_status', 'delivery_method', 'cancellation_reason', 'updated_at'];
    $order_columns = [];
    $orders_check->data_seek(0);
    while ($row = $orders_check->fetch_assoc()) {
        $order_columns[] = $row['Field'];
    }
    
    $missing_order_cols = array_diff($required_order_columns, $order_columns);
    if (empty($missing_order_cols)) {
        echo "<p style='color: green;'>✓ All required order columns present</p>";
    } else {
        echo "<p style='color: orange;'>⚠ Missing order columns: " . implode(', ', $missing_order_cols) . "</p>";
        echo "<p>Run the add_cancel_edit_functionality.sql script to add missing columns.</p>";
    }
} else {
    echo "<p style='color: red;'>✗ Orders table not found</p>";
}

// Test appointment functionality
echo "<h3>📅 Appointment Functionality Test:</h3>";
$appointment_count = $conn->query("SELECT COUNT(*) as count FROM appointments")->fetch_assoc()['count'];
echo "<p>Total appointments: $appointment_count</p>";

if ($appointment_count > 0) {
    // Show sample appointments with different statuses
    $sample_appointments = $conn->query("
        SELECT a.appointment_id, a.status, a.appointment_date, d.full_name as doctor_name
        FROM appointments a 
        JOIN doctors d ON a.doctor_id = d.doctor_id 
        ORDER BY a.appointment_date DESC 
        LIMIT 5
    ");
    
    echo "<h4>Sample Appointments:</h4>";
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
    echo "<tr><th>ID</th><th>Doctor</th><th>Date</th><th>Status</th><th>Actions Available</th></tr>";
    while ($apt = $sample_appointments->fetch_assoc()) {
        $apt_date = new DateTime($apt['appointment_date']);
        $is_upcoming = $apt_date > new DateTime();
        $can_modify = $apt['status'] === 'Scheduled' && $is_upcoming;
        
        echo "<tr>";
        echo "<td>" . $apt['appointment_id'] . "</td>";
        echo "<td>Dr. " . htmlspecialchars($apt['doctor_name']) . "</td>";
        echo "<td>" . $apt_date->format('M j, Y g:i A') . "</td>";
        echo "<td>" . $apt['status'] . "</td>";
        echo "<td>" . ($can_modify ? "Edit, Cancel" : "View only") . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color: orange;'>⚠ No appointments found. Create some appointments to test the functionality.</p>";
}

// Test order functionality
echo "<h3>🛒 Order Functionality Test:</h3>";
$order_count = $conn->query("SELECT COUNT(*) as count FROM orders")->fetch_assoc()['count'];
echo "<p>Total orders: $order_count</p>";

if ($order_count > 0) {
    // Show sample orders with different statuses
    $sample_orders = $conn->query("
        SELECT order_id, order_status, delivery_method, total, order_date
        FROM orders 
        ORDER BY order_date DESC 
        LIMIT 5
    ");
    
    echo "<h4>Sample Orders:</h4>";
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
    echo "<tr><th>ID</th><th>Status</th><th>Delivery</th><th>Total</th><th>Date</th><th>Actions Available</th></tr>";
    while ($order = $sample_orders->fetch_assoc()) {
        $can_cancel = in_array($order['order_status'], ['Pending', 'Processing']);
        $can_edit_delivery = $order['order_status'] === 'Pending';
        
        $actions = [];
        if ($can_edit_delivery) $actions[] = "Change Delivery";
        if ($can_cancel) $actions[] = "Cancel";
        if (empty($actions)) $actions[] = "View only";
        
        echo "<tr>";
        echo "<td>" . $order['order_id'] . "</td>";
        echo "<td>" . $order['order_status'] . "</td>";
        echo "<td>" . $order['delivery_method'] . "</td>";
        echo "<td>Rs. " . number_format($order['total'], 2) . "</td>";
        echo "<td>" . date('M j, Y g:i A', strtotime($order['order_date'])) . "</td>";
        echo "<td>" . implode(', ', $actions) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color: orange;'>⚠ No orders found. Create some orders to test the functionality.</p>";
}

// Test SQL queries used by the enhanced pages
echo "<h3>🔍 SQL Query Tests:</h3>";

// Test enhanced appointment query
try {
    $test_apt_sql = "
        SELECT a.appointment_id, a.appointment_date, a.consultation_type, a.status, a.cancellation_reason,
               d.full_name AS Doctor, d.specialty, a.diagnosis, d.doctor_id
        FROM appointments a
        JOIN doctors d ON a.doctor_id=d.doctor_id
        LIMIT 1
    ";
    $test_apt_result = $conn->query($test_apt_sql);
    if ($test_apt_result) {
        echo "<p style='color: green;'>✓ Enhanced appointment query works correctly</p>";
    } else {
        echo "<p style='color: red;'>✗ Appointment query failed: " . $conn->error . "</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Appointment query error: " . $e->getMessage() . "</p>";
}

// Test enhanced order query
try {
    $test_order_sql = "
        SELECT o.order_id, o.order_date, o.total, o.order_status, o.delivery_method, o.cancellation_reason,
               oi.quantity, oi.price, p.product_name, p.category
        FROM orders o
        JOIN order_items oi ON o.order_id=oi.order_id
        JOIN products p ON oi.product_id=p.product_id
        LIMIT 1
    ";
    $test_order_result = $conn->query($test_order_sql);
    if ($test_order_result) {
        echo "<p style='color: green;'>✓ Enhanced order query works correctly</p>";
    } else {
        echo "<p style='color: red;'>✗ Order query failed: " . $conn->error . "</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Order query error: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<h3>🔗 Test Links:</h3>";
echo "<p><a href='view_appointments.php'>Test Enhanced Appointments Page</a></p>";
echo "<p><a href='view_orders.php'>Test Enhanced Orders Page</a></p>";
echo "<p><a href='book_appointment.php'>Book Test Appointment</a></p>";
echo "<p><a href='order_product.php'>Place Test Order</a></p>";
echo "<p><a href='index.php'>Back to Main Site</a></p>";

echo "<hr>";
echo "<h3>📝 Setup Instructions:</h3>";
echo "<ol>";
echo "<li>Run the <strong>add_cancel_edit_functionality.sql</strong> script to ensure proper database structure</li>";
echo "<li>Create some test appointments and orders</li>";
echo "<li>Test the cancel and edit functionality on the enhanced pages</li>";
echo "</ol>";

?>

<style>
body { 
    font-family: Arial, sans-serif; 
    margin: 20px; 
    background: #f5f5f5;
}
table { 
    background: white; 
    border: 1px solid #ddd;
}
th { 
    background: #2c5aa0; 
    color: white; 
    padding: 10px;
}
td { 
    padding: 8px;
}
h2, h3, h4 { 
    color: #2c5aa0;
}
</style>
