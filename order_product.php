<?php
include 'includes/header.php';
if (!isset($_SESSION['user_id'])) {
  header("Location: login.php"); exit;
}

// Get products with categories
$prods = $conn->query("SELECT product_id, product_name, price, stock, category, description FROM products WHERE stock > 0 ORDER BY category, product_name");
?>

<div class="form-container">
  <h2>🛒 Order Health Products</h2>
  <p style="text-align: center; margin-bottom: 2rem; color: #666;">
    Browse and order quality health products and medicines
  </p>

  <!-- Display available products -->
  <div style="margin-bottom: 2rem;">
    <h3 style="color: #2c5aa0; margin-bottom: 1rem;">📦 Available Products</h3>
    <div style="display: grid; gap: 1rem; margin-bottom: 2rem;">
      <?php
      $prods_display = $conn->query("SELECT product_id, product_name, price, stock, category, description FROM products WHERE stock > 0 ORDER BY category, product_name");
      $current_category = '';
      while($p = $prods_display->fetch_assoc()):
        if ($current_category != $p['category']):
          if ($current_category != '') echo '</div>';
          $current_category = $p['category'];
          echo "<h4 style='color: #666; margin: 1rem 0 0.5rem 0;'>{$p['category']}</h4>";
          echo '<div style="display: grid; gap: 0.5rem;">';
        endif;
      ?>
        <div style="background: #f8f9fa; padding: 1rem; border-radius: 8px; border-left: 4px solid #2c5aa0;">
          <strong><?= htmlspecialchars($p['product_name']) ?></strong>
          <span style="color: #28a745; font-weight: bold; float: right;">Rs. <?= number_format($p['price'], 2) ?></span>
          <br>
          <small style="color: #666;"><?= htmlspecialchars($p['description']) ?></small>
          <br>
          <small style="color: #007bff;">Stock: <?= $p['stock'] ?> available</small>
        </div>
      <?php endwhile; ?>
      </div>
  </div>

  <form method="post" action="">
    <label style="font-weight: 600; margin-bottom: 0.5rem; display: block; color: #2c5aa0;">
      💊 Select Product
    </label>
    <select name="product_id" required style="padding-left: 40px;">
      <option value="">Choose a product...</option>
      <?php
      $prods->data_seek(0); // Reset result pointer
      while($p=$prods->fetch_assoc()):
      ?>
        <option value="<?= $p['product_id'] ?>">
          <?= htmlspecialchars($p['product_name']) ?> - Rs. <?= number_format($p['price'], 2) ?> (<?= $p['stock'] ?> in stock)
        </option>
      <?php endwhile; ?>
    </select>

    <label style="font-weight: 600; margin-bottom: 0.5rem; display: block; color: #2c5aa0;">
      🔢 Quantity
    </label>
    <input name="quantity" type="number" min="1" max="10" value="1" required style="padding-left: 40px;" placeholder="Enter quantity">

    <label style="font-weight: 600; margin-bottom: 0.5rem; display: block; color: #2c5aa0;">
      🚚 Delivery Method
    </label>
    <select name="delivery_method" style="padding-left: 40px;">
      <option value="Pickup">🏪 Pickup from Store (Free)</option>
      <option value="Delivery">🚚 Home Delivery (+Rs. 100)</option>
    </select>

    <button type="submit" class="btn-primary">🛒 Place Order</button>
  </form>

  <p style="text-align: center; margin-top: 2rem;">
    <a href="view_orders.php" style="color: #2c5aa0; text-decoration: none;">📦 View My Orders</a>
  </p>
</div>
<?php
if ($_SERVER['REQUEST_METHOD']==='POST') {
  // Get product price
  $price_query = $conn->prepare("SELECT price FROM products WHERE product_id = ?");
  $price_query->bind_param('i', $_POST['product_id']);
  $price_query->execute();
  $price_result = $price_query->get_result();
  $product = $price_result->fetch_assoc();

  if ($product) {
    $total = $product['price'] * $_POST['quantity'];

    // Add delivery fee if applicable
    $delivery_fee = ($_POST['delivery_method'] == 'Delivery') ? 100 : 0;
    $total += $delivery_fee;

    // Create order
    $st = $conn->prepare(
      "INSERT INTO orders (customer_id, total, order_date, delivery_method, order_status)
       VALUES (?, ?, NOW(), ?, 'Pending')"
    );
    $st->bind_param('ids', $_SESSION['user_id'], $total, $_POST['delivery_method']);

    if ($st->execute()) {
      $order_id = $st->insert_id;

      // Add order item
      $item_st = $conn->prepare(
        "INSERT INTO order_items (order_id, product_id, quantity, price)
         VALUES (?, ?, ?, ?)"
      );
      $item_st->bind_param('iiid', $order_id, $_POST['product_id'], $_POST['quantity'], $product['price']);

      if ($item_st->execute()) {
        echo "<p class='success'>Order placed!</p>";

        // Update loyalty status
        $order_count_query = $conn->prepare("SELECT COUNT(*) as count FROM orders WHERE customer_id = ?");
        $order_count_query->bind_param('i', $_SESSION['user_id']);
        $order_count_query->execute();
        $count_result = $order_count_query->get_result();
        $order_count = $count_result->fetch_assoc()['count'];

        if ($order_count >= 5) {
          $u = $conn->prepare("UPDATE patients SET loyalty_status='Loyal' WHERE patient_id=?");
          $u->bind_param('i', $_SESSION['user_id']);
          $u->execute();
        }
      } else {
        echo "<p class='error'>Error adding order item: " . $item_st->error . "</p>";
      }
    } else {
      echo "<p class='error'>Error creating order: " . $st->error . "</p>";
    }
  } else {
    echo "<p class='error'>Product not found!</p>";
  }
}
?>
<?php include 'includes/footer.php'; ?>
