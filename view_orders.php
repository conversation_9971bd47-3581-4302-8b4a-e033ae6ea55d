<?php
include 'includes/header.php';
if (!isset($_SESSION['user_id'])) {
  header("Location: login.php"); exit;
}

// Function to display order details
function displayOrder($order_info, $order_items) {
  $order_date = new DateTime($order_info['order_date']);
  $status_colors = [
    'Pending' => '#ffc107',
    'Processing' => '#17a2b8',
    'Shipped' => '#007bff',
    'Delivered' => '#28a745',
    'Cancelled' => '#dc3545'
  ];
  $status_color = $status_colors[$order_info['order_status']] ?? '#6c757d';
?>
  <div style="background: white; border-radius: 10px; padding: 1.5rem; box-shadow: 0 2px 10px rgba(0,0,0,0.1); border-left: 4px solid <?= $status_color ?>;">
    <div style="display: grid; grid-template-columns: 1fr auto; gap: 1rem; align-items: start; margin-bottom: 1rem;">
      <div>
        <h3 style="color: #2c5aa0; margin-bottom: 0.5rem;">
          📦 Order #<?= $order_info['order_id'] ?>
        </h3>
        <p style="color: #666; margin-bottom: 0.5rem;">
          <strong>Date:</strong> <?= $order_date->format('F j, Y \a\t g:i A') ?>
        </p>
        <p style="color: #666; margin-bottom: 0.5rem;">
          <strong>Total:</strong> Rs. <?= number_format($order_info['total'], 2) ?>
        </p>
      </div>
      <div style="text-align: center;">
        <span style="background: <?= $status_color ?>; color: white; padding: 0.5rem 1rem; border-radius: 20px; font-size: 0.9rem;">
          <?= $order_info['order_status'] ?>
        </span>
      </div>
    </div>

    <div style="background: #f8f9fa; padding: 1rem; border-radius: 5px;">
      <strong style="color: #2c5aa0;">Items Ordered:</strong>
      <div style="margin-top: 0.5rem;">
        <?php foreach($order_items as $item): ?>
          <div style="display: flex; justify-content: space-between; align-items: center; padding: 0.5rem 0; border-bottom: 1px solid #e9ecef;">
            <div>
              <strong><?= htmlspecialchars($item['product_name']) ?></strong>
              <br>
              <small style="color: #666;"><?= htmlspecialchars($item['category']) ?></small>
            </div>
            <div style="text-align: right;">
              <div>Qty: <?= $item['quantity'] ?></div>
              <div style="color: #28a745; font-weight: bold;">Rs. <?= number_format($item['price'], 2) ?></div>
            </div>
          </div>
        <?php endforeach; ?>
      </div>
    </div>
  </div>
<?php
}

$id = $_SESSION['user_id'];
$sql = "
  SELECT o.order_id, o.order_date, o.total, o.order_status, oi.quantity, oi.price, p.product_name, p.category
  FROM orders o
  JOIN order_items oi ON o.order_id=oi.order_id
  JOIN products p ON oi.product_id=p.product_id
  WHERE o.customer_id=?
  ORDER BY o.order_date DESC
";
$st = $conn->prepare($sql);
$st->bind_param('i',$id);
$st->execute();
$res = $st->get_result();
?>

<div style="max-width: 1000px; margin: 0 auto; padding: 2rem;">
  <h2 style="text-align: center; color: #2c5aa0; margin-bottom: 2rem;">📦 My Orders</h2>

  <div style="text-align: center; margin-bottom: 2rem;">
    <a href="order_product.php" class="btn btn-primary">🛒 Order New Products</a>
  </div>

  <?php if ($res->num_rows > 0): ?>
    <div style="display: grid; gap: 1.5rem;">
      <?php
      $current_order_id = null;
      $order_items = [];
      $order_info = [];

      // Group items by order
      while($r = $res->fetch_assoc()) {
        if ($current_order_id != $r['order_id']) {
          if ($current_order_id !== null) {
            // Display previous order
            displayOrder($order_info, $order_items);
          }
          $current_order_id = $r['order_id'];
          $order_info = [
            'order_id' => $r['order_id'],
            'order_date' => $r['order_date'],
            'total' => $r['total'],
            'order_status' => $r['order_status']
          ];
          $order_items = [];
        }
        $order_items[] = [
          'product_name' => $r['product_name'],
          'category' => $r['category'],
          'quantity' => $r['quantity'],
          'price' => $r['price']
        ];
      }

      // Display last order
      if ($current_order_id !== null) {
        displayOrder($order_info, $order_items);
      }
      ?>
    </div>
  <?php else: ?>
    <div style="text-align: center; padding: 3rem; background: #f8f9fa; border-radius: 10px;">
      <div style="font-size: 4rem; margin-bottom: 1rem;">🛒</div>
      <h3 style="color: #666; margin-bottom: 1rem;">No Orders Yet</h3>
      <p style="color: #666; margin-bottom: 2rem;">You haven't placed any orders yet. Browse our health products and place your first order!</p>
      <a href="order_product.php" class="btn btn-primary">🛒 Browse Products</a>
    </div>
  <?php endif; ?>
</div>

<?php include 'includes/footer.php'; ?>
