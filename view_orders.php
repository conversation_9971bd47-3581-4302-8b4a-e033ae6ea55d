<?php
include 'includes/header.php';
if (!isset($_SESSION['user_id'])) {
  header("Location: login.php"); exit;
}

// Handle order actions (cancel/update)
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
  if (isset($_POST['action'])) {
    $order_id = $_POST['order_id'];

    if ($_POST['action'] === 'cancel') {
      $reason = $_POST['cancellation_reason'] ?? 'No reason provided';
      $cancel_stmt = $conn->prepare("UPDATE orders SET order_status='Cancelled', cancellation_reason=?, updated_at=NOW() WHERE order_id=? AND customer_id=? AND order_status IN ('Pending', 'Processing')");
      $cancel_stmt->bind_param('sii', $reason, $order_id, $_SESSION['user_id']);

      if ($cancel_stmt->execute()) {
        echo "<div class='alert alert-success'>Order cancelled successfully!</div>";
      } else {
        echo "<div class='alert alert-error'>Error cancelling order: " . $cancel_stmt->error . "</div>";
      }
    }

    if ($_POST['action'] === 'update_delivery') {
      $new_delivery_method = $_POST['new_delivery_method'];

      // Calculate new total based on delivery method
      $order_query = $conn->prepare("SELECT total, delivery_method FROM orders WHERE order_id=? AND customer_id=?");
      $order_query->bind_param('ii', $order_id, $_SESSION['user_id']);
      $order_query->execute();
      $order_result = $order_query->get_result();
      $order_data = $order_result->fetch_assoc();

      if ($order_data) {
        $current_total = $order_data['total'];
        $current_delivery = $order_data['delivery_method'];

        // Adjust total based on delivery method change
        if ($current_delivery === 'Delivery' && $new_delivery_method === 'Pickup') {
          $new_total = $current_total - 100; // Remove delivery fee
        } elseif ($current_delivery === 'Pickup' && $new_delivery_method === 'Delivery') {
          $new_total = $current_total + 100; // Add delivery fee
        } else {
          $new_total = $current_total; // No change
        }

        $update_stmt = $conn->prepare("UPDATE orders SET delivery_method=?, total=?, updated_at=NOW() WHERE order_id=? AND customer_id=? AND order_status='Pending'");
        $update_stmt->bind_param('sdii', $new_delivery_method, $new_total, $order_id, $_SESSION['user_id']);

        if ($update_stmt->execute()) {
          echo "<div class='alert alert-success'>Order delivery method updated successfully!</div>";
        } else {
          echo "<div class='alert alert-error'>Error updating order: " . $update_stmt->error . "</div>";
        }
      }
    }
  }
}

// Function to display order details
function displayOrder($order_info, $order_items) {
  $order_date = new DateTime($order_info['order_date']);
  $status_colors = [
    'Pending' => '#ffc107',
    'Processing' => '#17a2b8',
    'Shipped' => '#007bff',
    'Delivered' => '#28a745',
    'Cancelled' => '#dc3545'
  ];
  $status_color = $status_colors[$order_info['order_status']] ?? '#6c757d';

  $can_cancel = in_array($order_info['order_status'], ['Pending', 'Processing']);
  $can_edit_delivery = $order_info['order_status'] === 'Pending';
  $is_cancelled = $order_info['order_status'] === 'Cancelled';
?>
  <div style="background: white; border-radius: 10px; padding: 1.5rem; box-shadow: 0 2px 10px rgba(0,0,0,0.1); border-left: 4px solid <?= $status_color ?>;">
    <div style="display: grid; grid-template-columns: 1fr auto; gap: 1rem; align-items: start; margin-bottom: 1rem;">
      <div>
        <h3 style="color: #2c5aa0; margin-bottom: 0.5rem;">
          📦 Order #<?= $order_info['order_id'] ?>
        </h3>
        <p style="color: #666; margin-bottom: 0.5rem;">
          <strong>Date:</strong> <?= $order_date->format('F j, Y \a\t g:i A') ?>
        </p>
        <p style="color: #666; margin-bottom: 0.5rem;">
          <strong>Total:</strong> Rs. <?= number_format($order_info['total'], 2) ?>
        </p>
        <p style="color: #666; margin-bottom: 0.5rem;">
          <strong>Delivery:</strong> <?= $order_info['delivery_method'] ?>
          <?php if ($order_info['delivery_method'] === 'Delivery'): ?>
            <small style="color: #28a745;">(+Rs. 100 delivery fee)</small>
          <?php else: ?>
            <small style="color: #007bff;">(Free pickup)</small>
          <?php endif; ?>
        </p>

        <?php if ($is_cancelled && !empty($order_info['cancellation_reason'])): ?>
          <div style="background: #f8d7da; padding: 1rem; border-radius: 5px; margin-top: 1rem; border: 1px solid #f5c6cb;">
            <strong style="color: #721c24;">Cancellation Reason:</strong>
            <p style="margin-top: 0.5rem; color: #721c24;"><?= htmlspecialchars($order_info['cancellation_reason']) ?></p>
          </div>
        <?php endif; ?>

        <?php if ($can_cancel || $can_edit_delivery): ?>
          <div style="margin-top: 1rem; display: flex; gap: 0.5rem; flex-wrap: wrap;">
            <?php if ($can_edit_delivery): ?>
              <button onclick="showDeliveryEditForm(<?= $order_info['order_id'] ?>)" class="btn btn-secondary" style="font-size: 0.9rem; padding: 0.5rem 1rem;">
                🚚 Change Delivery
              </button>
            <?php endif; ?>
            <?php if ($can_cancel): ?>
              <button onclick="showOrderCancelForm(<?= $order_info['order_id'] ?>)" class="btn btn-danger" style="font-size: 0.9rem; padding: 0.5rem 1rem;">
                ❌ Cancel Order
              </button>
            <?php endif; ?>
          </div>
        <?php endif; ?>
      </div>
      <div style="text-align: center;">
        <span style="background: <?= $status_color ?>; color: white; padding: 0.5rem 1rem; border-radius: 20px; font-size: 0.9rem;">
          <?= $order_info['order_status'] ?>
        </span>
      </div>
    </div>

    <div style="background: #f8f9fa; padding: 1rem; border-radius: 5px;">
      <strong style="color: #2c5aa0;">Items Ordered:</strong>
      <div style="margin-top: 0.5rem;">
        <?php foreach($order_items as $item): ?>
          <div style="display: flex; justify-content: space-between; align-items: center; padding: 0.5rem 0; border-bottom: 1px solid #e9ecef;">
            <div>
              <strong><?= htmlspecialchars($item['product_name']) ?></strong>
              <br>
              <small style="color: #666;"><?= htmlspecialchars($item['category']) ?></small>
            </div>
            <div style="text-align: right;">
              <div>Qty: <?= $item['quantity'] ?></div>
              <div style="color: #28a745; font-weight: bold;">Rs. <?= number_format($item['price'], 2) ?></div>
            </div>
          </div>
        <?php endforeach; ?>
      </div>
    </div>

    <!-- Delivery Edit Form (Hidden by default) -->
    <div id="delivery-edit-form-<?= $order_info['order_id'] ?>" style="display: none; margin-top: 1.5rem; padding: 1rem; background: #e7f3ff; border-radius: 5px; border: 1px solid #b3d9ff;">
      <h4 style="color: #0056b3; margin-bottom: 1rem;">Change Delivery Method</h4>
      <form method="post" action="">
        <input type="hidden" name="action" value="update_delivery">
        <input type="hidden" name="order_id" value="<?= $order_info['order_id'] ?>">

        <div style="margin-bottom: 1rem;">
          <label style="font-weight: 600; margin-bottom: 0.5rem; display: block;">Delivery Method:</label>
          <select name="new_delivery_method" style="width: 100%; padding: 0.5rem;">
            <option value="Pickup" <?= $order_info['delivery_method'] === 'Pickup' ? 'selected' : '' ?>>🏪 Pickup from Store (Free)</option>
            <option value="Delivery" <?= $order_info['delivery_method'] === 'Delivery' ? 'selected' : '' ?>>🚚 Home Delivery (+Rs. 100)</option>
          </select>
          <small style="color: #666; margin-top: 0.5rem; display: block;">Note: Changing to delivery will add Rs. 100 to your total. Changing to pickup will remove the delivery fee.</small>
        </div>

        <div style="display: flex; gap: 0.5rem;">
          <button type="submit" class="btn btn-primary" style="font-size: 0.9rem;">💾 Update Delivery</button>
          <button type="button" onclick="hideDeliveryEditForm(<?= $order_info['order_id'] ?>)" class="btn btn-secondary" style="font-size: 0.9rem;">❌ Cancel</button>
        </div>
      </form>
    </div>

    <!-- Order Cancel Form (Hidden by default) -->
    <div id="order-cancel-form-<?= $order_info['order_id'] ?>" style="display: none; margin-top: 1.5rem; padding: 1rem; background: #f8d7da; border-radius: 5px; border: 1px solid #f5c6cb;">
      <h4 style="color: #721c24; margin-bottom: 1rem;">Cancel Order</h4>
      <form method="post" action="">
        <input type="hidden" name="action" value="cancel">
        <input type="hidden" name="order_id" value="<?= $order_info['order_id'] ?>">

        <div style="margin-bottom: 1rem;">
          <label style="font-weight: 600; margin-bottom: 0.5rem; display: block;">Reason for cancellation:</label>
          <textarea name="cancellation_reason" rows="3" style="width: 100%; padding: 0.5rem;" placeholder="Please provide a reason for cancelling this order..."></textarea>
        </div>

        <div style="display: flex; gap: 0.5rem;">
          <button type="submit" class="btn btn-danger" style="font-size: 0.9rem;" onclick="return confirm('Are you sure you want to cancel this order?')">❌ Confirm Cancellation</button>
          <button type="button" onclick="hideOrderCancelForm(<?= $order_info['order_id'] ?>)" class="btn btn-secondary" style="font-size: 0.9rem;">↩️ Keep Order</button>
        </div>
      </form>
    </div>
  </div>
<?php
}

$id = $_SESSION['user_id'];
$sql = "
  SELECT o.order_id, o.order_date, o.total, o.order_status, o.delivery_method, o.cancellation_reason,
         oi.quantity, oi.price, p.product_name, p.category
  FROM orders o
  JOIN order_items oi ON o.order_id=oi.order_id
  JOIN products p ON oi.product_id=p.product_id
  WHERE o.customer_id=?
  ORDER BY o.order_date DESC
";
$st = $conn->prepare($sql);
$st->bind_param('i',$id);
$st->execute();
$res = $st->get_result();
?>

<div style="max-width: 1000px; margin: 0 auto; padding: 2rem;">
  <h2 style="text-align: center; color: #2c5aa0; margin-bottom: 2rem;">📦 My Orders</h2>

  <div style="text-align: center; margin-bottom: 2rem;">
    <a href="order_product.php" class="btn btn-primary">🛒 Order New Products</a>
  </div>

  <?php if ($res->num_rows > 0): ?>
    <div style="display: grid; gap: 1.5rem;">
      <?php
      $current_order_id = null;
      $order_items = [];
      $order_info = [];

      // Group items by order
      while($r = $res->fetch_assoc()) {
        if ($current_order_id != $r['order_id']) {
          if ($current_order_id !== null) {
            // Display previous order
            displayOrder($order_info, $order_items);
          }
          $current_order_id = $r['order_id'];
          $order_info = [
            'order_id' => $r['order_id'],
            'order_date' => $r['order_date'],
            'total' => $r['total'],
            'order_status' => $r['order_status'],
            'delivery_method' => $r['delivery_method'],
            'cancellation_reason' => $r['cancellation_reason']
          ];
          $order_items = [];
        }
        $order_items[] = [
          'product_name' => $r['product_name'],
          'category' => $r['category'],
          'quantity' => $r['quantity'],
          'price' => $r['price']
        ];
      }

      // Display last order
      if ($current_order_id !== null) {
        displayOrder($order_info, $order_items);
      }
      ?>
    </div>
  <?php else: ?>
    <div style="text-align: center; padding: 3rem; background: #f8f9fa; border-radius: 10px;">
      <div style="font-size: 4rem; margin-bottom: 1rem;">🛒</div>
      <h3 style="color: #666; margin-bottom: 1rem;">No Orders Yet</h3>
      <p style="color: #666; margin-bottom: 2rem;">You haven't placed any orders yet. Browse our health products and place your first order!</p>
      <a href="order_product.php" class="btn btn-primary">🛒 Browse Products</a>
    </div>
  <?php endif; ?>
</div>

<script>
function showDeliveryEditForm(orderId) {
  // Hide all other forms first
  document.querySelectorAll('[id^="delivery-edit-form-"], [id^="order-cancel-form-"]').forEach(form => {
    form.style.display = 'none';
  });

  // Show the delivery edit form for this order
  document.getElementById('delivery-edit-form-' + orderId).style.display = 'block';
}

function hideDeliveryEditForm(orderId) {
  document.getElementById('delivery-edit-form-' + orderId).style.display = 'none';
}

function showOrderCancelForm(orderId) {
  // Hide all other forms first
  document.querySelectorAll('[id^="delivery-edit-form-"], [id^="order-cancel-form-"]').forEach(form => {
    form.style.display = 'none';
  });

  // Show the cancel form for this order
  document.getElementById('order-cancel-form-' + orderId).style.display = 'block';
}

function hideOrderCancelForm(orderId) {
  document.getElementById('order-cancel-form-' + orderId).style.display = 'none';
}
</script>

<style>
.alert {
  padding: 1rem;
  margin: 1rem 0;
  border-radius: 5px;
  font-weight: 600;
}

.alert-success {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.alert-error {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  text-decoration: none;
  display: inline-block;
  font-weight: 600;
  transition: all 0.3s ease;
}

.btn-primary {
  background-color: #2c5aa0;
  color: white;
}

.btn-primary:hover {
  background-color: #1e3f73;
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background-color: #545b62;
}

.btn-danger {
  background-color: #dc3545;
  color: white;
}

.btn-danger:hover {
  background-color: #c82333;
}
</style>

<?php include 'includes/footer.php'; ?>
