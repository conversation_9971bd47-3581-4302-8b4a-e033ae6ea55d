<?php
include 'includes/header.php';
if (!isset($_SESSION['user_id'])) {
  header("Location: login.php"); exit;
}

// Handle appointment actions (cancel/update)
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
  if (isset($_POST['action'])) {
    $appointment_id = $_POST['appointment_id'];

    if ($_POST['action'] === 'cancel') {
      $reason = $_POST['cancellation_reason'] ?? 'No reason provided';
      $cancel_stmt = $conn->prepare("UPDATE appointments SET status='Cancelled', cancellation_reason=?, updated_at=NOW() WHERE appointment_id=? AND patient_id=?");
      $cancel_stmt->bind_param('sii', $reason, $appointment_id, $_SESSION['user_id']);

      if ($cancel_stmt->execute()) {
        echo "<div class='alert alert-success'>Appointment cancelled successfully!</div>";
      } else {
        echo "<div class='alert alert-error'>Error cancelling appointment: " . $cancel_stmt->error . "</div>";
      }
    }

    if ($_POST['action'] === 'update') {
      $new_date = $_POST['new_date'];
      $new_time = $_POST['new_time'];
      $new_consultation_type = $_POST['new_consultation_type'];
      $new_datetime = $new_date . ' ' . $new_time;

      $update_stmt = $conn->prepare("UPDATE appointments SET appointment_date=?, consultation_type=?, updated_at=NOW() WHERE appointment_id=? AND patient_id=? AND status='Scheduled'");
      $update_stmt->bind_param('ssii', $new_datetime, $new_consultation_type, $appointment_id, $_SESSION['user_id']);

      if ($update_stmt->execute()) {
        echo "<div class='alert alert-success'>Appointment updated successfully!</div>";
      } else {
        echo "<div class='alert alert-error'>Error updating appointment: " . $update_stmt->error . "</div>";
      }
    }
  }
}

$id = $_SESSION['user_id'];
$sql = "
  SELECT a.appointment_id, a.appointment_date, a.consultation_type, a.status, a.cancellation_reason,
         d.full_name AS Doctor, d.specialty, a.diagnosis, d.doctor_id
  FROM appointments a
  JOIN doctors d ON a.doctor_id=d.doctor_id
  WHERE a.patient_id=?
  ORDER BY a.appointment_date DESC
";
$st = $conn->prepare($sql);
$st->bind_param('i',$id);
$st->execute();
$res = $st->get_result();
?>

<div style="max-width: 1000px; margin: 0 auto; padding: 2rem;">
  <h2 style="text-align: center; color: #2c5aa0; margin-bottom: 2rem;">📋 My Appointments</h2>

  <div style="text-align: center; margin-bottom: 2rem;">
    <a href="book_appointment.php" class="btn btn-primary">📅 Book New Appointment</a>
  </div>

  <?php if ($res->num_rows > 0): ?>
    <div style="display: grid; gap: 1.5rem;">
      <?php while($r=$res->fetch_assoc()):
        $appointment_date = new DateTime($r['appointment_date']);
        $is_upcoming = $appointment_date > new DateTime();
        $is_scheduled = $r['status'] === 'Scheduled';
        $is_cancelled = $r['status'] === 'Cancelled';

        // Determine status color and text
        if ($is_cancelled) {
          $status_color = '#dc3545';
          $status_text = 'Cancelled';
        } elseif ($is_upcoming && $is_scheduled) {
          $status_color = '#28a745';
          $status_text = 'Upcoming';
        } elseif ($r['status'] === 'Completed') {
          $status_color = '#6c757d';
          $status_text = 'Completed';
        } else {
          $status_color = '#ffc107';
          $status_text = $r['status'];
        }

        $can_modify = $is_scheduled && $is_upcoming;
      ?>
        <div style="background: white; border-radius: 10px; padding: 1.5rem; box-shadow: 0 2px 10px rgba(0,0,0,0.1); border-left: 4px solid <?= $status_color ?>;">
          <div style="display: grid; grid-template-columns: 1fr auto; gap: 1rem; align-items: start;">
            <div>
              <h3 style="color: #2c5aa0; margin-bottom: 0.5rem;">
                👨‍⚕️ Dr. <?= htmlspecialchars($r['Doctor']) ?>
              </h3>
              <p style="color: #666; margin-bottom: 0.5rem;">
                <strong>Specialty:</strong> <?= htmlspecialchars($r['specialty']) ?>
              </p>
              <p style="color: #666; margin-bottom: 0.5rem;">
                <strong>Consultation Type:</strong> <?= htmlspecialchars($r['consultation_type']) ?>
              </p>
              <p style="color: #666; margin-bottom: 0.5rem;">
                <strong>Date & Time:</strong> <?= $appointment_date->format('F j, Y \a\t g:i A') ?>
              </p>

              <?php if ($is_cancelled && !empty($r['cancellation_reason'])): ?>
                <div style="background: #f8d7da; padding: 1rem; border-radius: 5px; margin-top: 1rem; border: 1px solid #f5c6cb;">
                  <strong style="color: #721c24;">Cancellation Reason:</strong>
                  <p style="margin-top: 0.5rem; color: #721c24;"><?= htmlspecialchars($r['cancellation_reason']) ?></p>
                </div>
              <?php endif; ?>

              <?php if (!empty($r['diagnosis'])): ?>
                <div style="background: #f8f9fa; padding: 1rem; border-radius: 5px; margin-top: 1rem;">
                  <strong style="color: #2c5aa0;">Diagnosis:</strong>
                  <p style="margin-top: 0.5rem;"><?= htmlspecialchars($r['diagnosis']) ?></p>
                </div>
              <?php endif; ?>

              <?php if ($can_modify): ?>
                <div style="margin-top: 1rem; display: flex; gap: 0.5rem; flex-wrap: wrap;">
                  <button onclick="showEditForm(<?= $r['appointment_id'] ?>)" class="btn btn-secondary" style="font-size: 0.9rem; padding: 0.5rem 1rem;">
                    ✏️ Edit
                  </button>
                  <button onclick="showCancelForm(<?= $r['appointment_id'] ?>)" class="btn btn-danger" style="font-size: 0.9rem; padding: 0.5rem 1rem;">
                    ❌ Cancel
                  </button>
                </div>
              <?php endif; ?>
            </div>
            <div style="text-align: center;">
              <span style="background: <?= $status_color ?>; color: white; padding: 0.5rem 1rem; border-radius: 20px; font-size: 0.9rem;">
                <?= $status_text ?>
              </span>
            </div>
          </div>

          <!-- Edit Form (Hidden by default) -->
          <div id="edit-form-<?= $r['appointment_id'] ?>" style="display: none; margin-top: 1.5rem; padding: 1rem; background: #f8f9fa; border-radius: 5px;">
            <h4 style="color: #2c5aa0; margin-bottom: 1rem;">Edit Appointment</h4>
            <form method="post" action="">
              <input type="hidden" name="action" value="update">
              <input type="hidden" name="appointment_id" value="<?= $r['appointment_id'] ?>">

              <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; margin-bottom: 1rem;">
                <div>
                  <label style="font-weight: 600; margin-bottom: 0.5rem; display: block;">New Date:</label>
                  <input name="new_date" type="date" required min="<?= date('Y-m-d') ?>" value="<?= $appointment_date->format('Y-m-d') ?>" style="width: 100%; padding: 0.5rem;">
                </div>
                <div>
                  <label style="font-weight: 600; margin-bottom: 0.5rem; display: block;">New Time:</label>
                  <select name="new_time" required style="width: 100%; padding: 0.5rem;">
                    <option value="09:00" <?= $appointment_date->format('H:i') === '09:00' ? 'selected' : '' ?>>9:00 AM</option>
                    <option value="10:00" <?= $appointment_date->format('H:i') === '10:00' ? 'selected' : '' ?>>10:00 AM</option>
                    <option value="11:00" <?= $appointment_date->format('H:i') === '11:00' ? 'selected' : '' ?>>11:00 AM</option>
                    <option value="14:00" <?= $appointment_date->format('H:i') === '14:00' ? 'selected' : '' ?>>2:00 PM</option>
                    <option value="15:00" <?= $appointment_date->format('H:i') === '15:00' ? 'selected' : '' ?>>3:00 PM</option>
                    <option value="16:00" <?= $appointment_date->format('H:i') === '16:00' ? 'selected' : '' ?>>4:00 PM</option>
                    <option value="17:00" <?= $appointment_date->format('H:i') === '17:00' ? 'selected' : '' ?>>5:00 PM</option>
                  </select>
                </div>
              </div>

              <div style="margin-bottom: 1rem;">
                <label style="font-weight: 600; margin-bottom: 0.5rem; display: block;">Consultation Type:</label>
                <select name="new_consultation_type" style="width: 100%; padding: 0.5rem;">
                  <option value="General Consultation" <?= $r['consultation_type'] === 'General Consultation' ? 'selected' : '' ?>>General Consultation</option>
                  <option value="Follow-up" <?= $r['consultation_type'] === 'Follow-up' ? 'selected' : '' ?>>Follow-up</option>
                  <option value="Emergency" <?= $r['consultation_type'] === 'Emergency' ? 'selected' : '' ?>>Emergency</option>
                  <option value="Specialist Consultation" <?= $r['consultation_type'] === 'Specialist Consultation' ? 'selected' : '' ?>>Specialist Consultation</option>
                </select>
              </div>

              <div style="display: flex; gap: 0.5rem;">
                <button type="submit" class="btn btn-primary" style="font-size: 0.9rem;">💾 Save Changes</button>
                <button type="button" onclick="hideEditForm(<?= $r['appointment_id'] ?>)" class="btn btn-secondary" style="font-size: 0.9rem;">❌ Cancel</button>
              </div>
            </form>
          </div>

          <!-- Cancel Form (Hidden by default) -->
          <div id="cancel-form-<?= $r['appointment_id'] ?>" style="display: none; margin-top: 1.5rem; padding: 1rem; background: #f8d7da; border-radius: 5px; border: 1px solid #f5c6cb;">
            <h4 style="color: #721c24; margin-bottom: 1rem;">Cancel Appointment</h4>
            <form method="post" action="">
              <input type="hidden" name="action" value="cancel">
              <input type="hidden" name="appointment_id" value="<?= $r['appointment_id'] ?>">

              <div style="margin-bottom: 1rem;">
                <label style="font-weight: 600; margin-bottom: 0.5rem; display: block;">Reason for cancellation:</label>
                <textarea name="cancellation_reason" rows="3" style="width: 100%; padding: 0.5rem;" placeholder="Please provide a reason for cancelling this appointment..."></textarea>
              </div>

              <div style="display: flex; gap: 0.5rem;">
                <button type="submit" class="btn btn-danger" style="font-size: 0.9rem;" onclick="return confirm('Are you sure you want to cancel this appointment?')">❌ Confirm Cancellation</button>
                <button type="button" onclick="hideCancelForm(<?= $r['appointment_id'] ?>)" class="btn btn-secondary" style="font-size: 0.9rem;">↩️ Keep Appointment</button>
              </div>
            </form>
          </div>
        </div>
      <?php endwhile; ?>
    </div>
  <?php else: ?>
    <div style="text-align: center; padding: 3rem; background: #f8f9fa; border-radius: 10px;">
      <div style="font-size: 4rem; margin-bottom: 1rem;">📅</div>
      <h3 style="color: #666; margin-bottom: 1rem;">No Appointments Yet</h3>
      <p style="color: #666; margin-bottom: 2rem;">You haven't booked any appointments yet. Start by scheduling your first consultation!</p>
      <a href="book_appointment.php" class="btn btn-primary">📅 Book Your First Appointment</a>
    </div>
  <?php endif; ?>
</div>

<script>
function showEditForm(appointmentId) {
  // Hide all other forms first
  document.querySelectorAll('[id^="edit-form-"], [id^="cancel-form-"]').forEach(form => {
    form.style.display = 'none';
  });

  // Show the edit form for this appointment
  document.getElementById('edit-form-' + appointmentId).style.display = 'block';
}

function hideEditForm(appointmentId) {
  document.getElementById('edit-form-' + appointmentId).style.display = 'none';
}

function showCancelForm(appointmentId) {
  // Hide all other forms first
  document.querySelectorAll('[id^="edit-form-"], [id^="cancel-form-"]').forEach(form => {
    form.style.display = 'none';
  });

  // Show the cancel form for this appointment
  document.getElementById('cancel-form-' + appointmentId).style.display = 'block';
}

function hideCancelForm(appointmentId) {
  document.getElementById('cancel-form-' + appointmentId).style.display = 'none';
}
</script>

<style>
.alert {
  padding: 1rem;
  margin: 1rem 0;
  border-radius: 5px;
  font-weight: 600;
}

.alert-success {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.alert-error {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  text-decoration: none;
  display: inline-block;
  font-weight: 600;
  transition: all 0.3s ease;
}

.btn-primary {
  background-color: #2c5aa0;
  color: white;
}

.btn-primary:hover {
  background-color: #1e3f73;
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background-color: #545b62;
}

.btn-danger {
  background-color: #dc3545;
  color: white;
}

.btn-danger:hover {
  background-color: #c82333;
}
</style>

<?php include 'includes/footer.php'; ?>
