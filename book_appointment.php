<?php
include 'includes/header.php';
if (!isset($_SESSION['user_id'])) {
  header("Location: login.php"); exit;
}

// Get doctors with their specializations
$docs = $conn->query("SELECT doctor_id, full_name, specialty FROM doctors ORDER BY full_name");
?>

<div class="form-container">
  <h2>📅 Book Appointment</h2>
  <p style="text-align: center; margin-bottom: 2rem; color: #666;">
    Schedule a consultation with our qualified doctors
  </p>

  <form method="post" action="">
    <label style="font-weight: 600; margin-bottom: 0.5rem; display: block; color: #2c5aa0;">
      👨‍⚕️ Select Doctor
    </label>
    <select name="doctor_id" required style="padding-left: 40px;">
      <option value="">Choose a doctor...</option>
      <?php while($d=$docs->fetch_assoc()): ?>
        <option value="<?= $d['doctor_id'] ?>">
          Dr. <?= htmlspecialchars($d['full_name']) ?> - <?= htmlspecialchars($d['specialty']) ?>
        </option>
      <?php endwhile; ?>
    </select>

    <label style="font-weight: 600; margin-bottom: 0.5rem; display: block; color: #2c5aa0;">
      📅 Appointment Date
    </label>
    <input name="date" type="date" required min="<?= date('Y-m-d') ?>" style="padding-left: 40px;">

    <label style="font-weight: 600; margin-bottom: 0.5rem; display: block; color: #2c5aa0;">
      🕐 Preferred Time
    </label>
    <select name="time" required style="padding-left: 40px;">
      <option value="">Select time...</option>
      <option value="09:00">9:00 AM</option>
      <option value="10:00">10:00 AM</option>
      <option value="11:00">11:00 AM</option>
      <option value="14:00">2:00 PM</option>
      <option value="15:00">3:00 PM</option>
      <option value="16:00">4:00 PM</option>
      <option value="17:00">5:00 PM</option>
    </select>

    <label style="font-weight: 600; margin-bottom: 0.5rem; display: block; color: #2c5aa0;">
      🩺 Consultation Type
    </label>
    <select name="consultation_type" required style="padding-left: 40px;">
      <option value="">Select consultation type...</option>
      <option value="General Checkup">General Checkup</option>
      <option value="Follow-up">Follow-up Visit</option>
      <option value="Specialist Consultation">Specialist Consultation</option>
      <option value="Emergency">Emergency</option>
      <option value="Vaccination">Vaccination</option>
      <option value="Health Screening">Health Screening</option>
    </select>

    <button type="submit" class="btn-primary">📅 Book Appointment</button>
  </form>

  <p style="text-align: center; margin-top: 2rem;">
    <a href="view_appointments.php" style="color: #2c5aa0; text-decoration: none;">📋 View My Appointments</a>
  </p>
</div>
<?php
if ($_SERVER['REQUEST_METHOD']==='POST') {
  $appointment_datetime = $_POST['date'] . ' ' . $_POST['time'];
  $st = $conn->prepare(
    "INSERT INTO appointments
     (patient_id,doctor_id,appointment_date,consultation_type)
     VALUES (?,?,?,?)"
  );
  $st->bind_param('iiss',
    $_SESSION['user_id'],
    $_POST['doctor_id'],
    $appointment_datetime,
    $_POST['consultation_type']
  );
  if ($st->execute()) {
    echo "<p class='success'>Appointment booked!</p>";
  }
}
?>
<?php include 'includes/footer.php'; ?>
