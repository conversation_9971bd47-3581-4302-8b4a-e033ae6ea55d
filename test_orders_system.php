<?php
// Test script to verify orders system functionality
require_once 'includes/data.php';

echo "<h2>🛒 Orders System Test</h2>";

// Check if database connection is working
if ($conn->connect_error) {
    die("<p style='color: red;'>Connection failed: " . $conn->connect_error . "</p>");
}

echo "<p style='color: green;'>✓ Database connection successful</p>";

// Check if required tables exist
$required_tables = ['orders', 'order_items', 'products'];
$missing_tables = [];

foreach ($required_tables as $table) {
    $table_check = $conn->query("SHOW TABLES LIKE '$table'");
    if ($table_check->num_rows == 0) {
        $missing_tables[] = $table;
    }
}

if (!empty($missing_tables)) {
    echo "<p style='color: red;'>✗ Missing tables: " . implode(', ', $missing_tables) . "</p>";
    echo "<p>Please run the fix_orders_system.sql script first.</p>";
    exit;
}

echo "<p style='color: green;'>✓ All required tables exist</p>";

// Check table structures
echo "<h3>📋 Table Structures:</h3>";

// Orders table
echo "<h4>Orders Table:</h4>";
$structure = $conn->query("DESCRIBE orders");
echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
while ($row = $structure->fetch_assoc()) {
    echo "<tr>";
    echo "<td>" . $row['Field'] . "</td>";
    echo "<td>" . $row['Type'] . "</td>";
    echo "<td>" . $row['Null'] . "</td>";
    echo "<td>" . $row['Key'] . "</td>";
    echo "<td>" . $row['Default'] . "</td>";
    echo "</tr>";
}
echo "</table>";

// Order Items table
echo "<h4>Order Items Table:</h4>";
$structure = $conn->query("DESCRIBE order_items");
echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
while ($row = $structure->fetch_assoc()) {
    echo "<tr>";
    echo "<td>" . $row['Field'] . "</td>";
    echo "<td>" . $row['Type'] . "</td>";
    echo "<td>" . $row['Null'] . "</td>";
    echo "<td>" . $row['Key'] . "</td>";
    echo "<td>" . $row['Default'] . "</td>";
    echo "</tr>";
}
echo "</table>";

// Check if there are products in the table
$product_count = $conn->query("SELECT COUNT(*) as count FROM products")->fetch_assoc()['count'];
echo "<h3>📦 Products Count: $product_count</h3>";

if ($product_count == 0) {
    echo "<p style='color: orange;'>⚠ No products found in the table!</p>";
    echo "<p>Please run the fix_orders_system.sql script to add sample products.</p>";
} else {
    echo "<p style='color: green;'>✓ Products found in the table</p>";
    
    // Display sample products
    echo "<h3>📝 Sample Products:</h3>";
    $products = $conn->query("SELECT product_id, product_name, category, price, stock FROM products ORDER BY category, product_name LIMIT 5");
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
    echo "<tr><th>ID</th><th>Name</th><th>Category</th><th>Price</th><th>Stock</th></tr>";
    while ($product = $products->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $product['product_id'] . "</td>";
        echo "<td>" . htmlspecialchars($product['product_name']) . "</td>";
        echo "<td>" . htmlspecialchars($product['category']) . "</td>";
        echo "<td>Rs. " . number_format($product['price'], 2) . "</td>";
        echo "<td>" . $product['stock'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
}

// Check existing orders
$order_count = $conn->query("SELECT COUNT(*) as count FROM orders")->fetch_assoc()['count'];
echo "<h3>📋 Existing Orders Count: $order_count</h3>";

if ($order_count > 0) {
    echo "<h4>Recent Orders:</h4>";
    $orders = $conn->query("
        SELECT o.order_id, o.customer_id, o.total, o.order_date, o.order_status, 
               COUNT(oi.order_item_id) as item_count
        FROM orders o 
        LEFT JOIN order_items oi ON o.order_id = oi.order_id 
        GROUP BY o.order_id 
        ORDER BY o.order_date DESC 
        LIMIT 5
    ");
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
    echo "<tr><th>Order ID</th><th>Customer ID</th><th>Total</th><th>Date</th><th>Status</th><th>Items</th></tr>";
    while ($order = $orders->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $order['order_id'] . "</td>";
        echo "<td>" . $order['customer_id'] . "</td>";
        echo "<td>Rs. " . number_format($order['total'], 2) . "</td>";
        echo "<td>" . $order['order_date'] . "</td>";
        echo "<td>" . $order['order_status'] . "</td>";
        echo "<td>" . $order['item_count'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
}

// Test the SQL query used in view_orders.php
echo "<h3>🔍 Testing View Orders Query:</h3>";
try {
    $test_sql = "
        SELECT o.order_id, o.order_date, o.total, o.order_status, oi.quantity, oi.price, p.product_name, p.category
        FROM orders o
        JOIN order_items oi ON o.order_id=oi.order_id
        JOIN products p ON oi.product_id=p.product_id
        LIMIT 1
    ";
    $test_result = $conn->query($test_sql);
    if ($test_result) {
        echo "<p style='color: green;'>✓ View orders query works correctly</p>";
        if ($test_result->num_rows > 0) {
            echo "<p>Sample query result found - view_orders.php should work properly</p>";
        } else {
            echo "<p style='color: orange;'>⚠ No order data found - create some test orders first</p>";
        }
    } else {
        echo "<p style='color: red;'>✗ Query failed: " . $conn->error . "</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Query error: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<h3>🔗 Quick Links:</h3>";
echo "<p><a href='order_product.php'>Test Product Ordering</a></p>";
echo "<p><a href='view_orders.php'>Test View Orders</a></p>";
echo "<p><a href='index.php'>Back to Main Site</a></p>";

echo "<hr>";
echo "<h3>📝 Setup Instructions:</h3>";
echo "<ol>";
echo "<li>Run the <strong>fix_orders_system.sql</strong> script to create the correct table structure</li>";
echo "<li>Test ordering products through <strong>order_product.php</strong></li>";
echo "<li>View orders through <strong>view_orders.php</strong></li>";
echo "</ol>";

?>

<style>
body { 
    font-family: Arial, sans-serif; 
    margin: 20px; 
    background: #f5f5f5;
}
table { 
    background: white; 
    border: 1px solid #ddd;
}
th { 
    background: #2c5aa0; 
    color: white; 
    padding: 10px;
}
td { 
    padding: 8px;
}
h2, h3, h4 { 
    color: #2c5aa0;
}
</style>
