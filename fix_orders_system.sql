-- Fix Orders System Database Schema
-- This script creates the correct tables for the orders system with proper naming conventions

-- Drop existing orders-related tables if they exist (in correct order to handle foreign keys)
DROP TABLE IF EXISTS order_items;
DROP TABLE IF EXISTS orders;

-- Create orders table with correct structure (using snake_case naming to match PHP code)
CREATE TABLE orders (
    order_id INT AUTO_INCREMENT PRIMARY KEY,
    customer_id INT NOT NULL,
    total DECIMAL(10,2) NOT NULL,
    order_date DATETIME DEFAULT CURRENT_TIMESTAMP,
    delivery_method ENUM('Pickup', 'Delivery') DEFAULT 'Pickup',
    order_status ENUM('Pending', 'Processing', 'Shipped', 'Delivered', 'Cancelled') DEFAULT 'Pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (customer_id) REFERENCES patients(patient_id) ON DELETE CASCADE
);

-- Create order_items table (separate table for order line items)
CREATE TABLE order_items (
    order_item_id INT AUTO_INCREMENT PRIMARY KEY,
    order_id INT NOT NULL,
    product_id INT NOT NULL,
    quantity INT NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (order_id) REFERENCES orders(order_id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(product_id) ON DELETE CASCADE
);

-- Ensure products table exists with correct structure
CREATE TABLE IF NOT EXISTS products (
    product_id INT AUTO_INCREMENT PRIMARY KEY,
    product_name VARCHAR(100) NOT NULL,
    description TEXT,
    price DECIMAL(10,2) NOT NULL,
    stock INT DEFAULT 0,
    category VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert sample products if the table is empty
INSERT IGNORE INTO products (product_name, category, price, stock, description) VALUES
('Paracetamol 500mg', 'Medicine', 150.00, 100, 'Pain relief and fever reducer'),
('Vitamin C Tablets', 'Supplements', 250.00, 50, 'Immune system support'),
('Digital Thermometer', 'Medical Devices', 1200.00, 25, 'Accurate temperature measurement'),
('Hand Sanitizer 250ml', 'Hygiene', 300.00, 75, 'Antibacterial hand sanitizer'),
('Face Masks (Pack of 10)', 'Protection', 500.00, 40, 'Disposable surgical masks'),
('Ibuprofen 400mg', 'Medicine', 180.00, 80, 'Anti-inflammatory pain relief'),
('Multivitamin Tablets', 'Supplements', 350.00, 60, 'Daily vitamin supplement'),
('Blood Pressure Monitor', 'Medical Devices', 2500.00, 15, 'Digital blood pressure monitor'),
('Antiseptic Cream', 'Medicine', 120.00, 90, 'Topical antiseptic for cuts and wounds'),
('Omega-3 Fish Oil', 'Supplements', 450.00, 40, 'Heart and brain health supplement');

-- Create a sample order for testing (optional - remove if you don't want test data)
-- INSERT INTO orders (customer_id, total, order_status) VALUES (1, 300.00, 'Pending');
-- INSERT INTO order_items (order_id, product_id, quantity, price) VALUES (1, 1, 2, 150.00);

-- Show the final structure
SELECT 'Orders table structure:' as info;
DESCRIBE orders;

SELECT 'Order_items table structure:' as info;
DESCRIBE order_items;

SELECT 'Products table structure:' as info;
DESCRIBE products;

SELECT 'Sample products data:' as info;
SELECT product_id, product_name, category, price, stock FROM products ORDER BY category, product_name;
