-- Add Cancel and Edit Functionality Database Updates
-- This script ensures proper structure for appointment and order management

-- Ensure appointments table has correct structure with snake_case naming
CREATE TABLE IF NOT EXISTS appointments (
    appointment_id INT AUTO_INCREMENT PRIMARY KEY,
    patient_id INT NOT NULL,
    doctor_id INT NOT NULL,
    appointment_date DATETIME NOT NULL,
    consultation_type VARCHAR(100),
    diagnosis TEXT,
    status ENUM('Scheduled', 'Completed', 'Cancelled') DEFAULT 'Scheduled',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (patient_id) REFERENCES patients(patient_id) ON DELETE CASCADE,
    FOREIGN KEY (doctor_id) REFERENCES doctors(doctor_id) ON DELETE CASCADE
);

-- Add updated_at column to orders table if it doesn't exist
ALTER TABLE orders ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;

-- Add cancellation reason columns for better tracking
ALTER TABLE appointments ADD COLUMN IF NOT EXISTS cancellation_reason TEXT;
ALTER TABLE orders ADD COLUMN IF NOT EXISTS cancellation_reason TEXT;

-- Create a table to track appointment/order modifications for audit purposes
CREATE TABLE IF NOT EXISTS modification_log (
    log_id INT AUTO_INCREMENT PRIMARY KEY,
    table_name ENUM('appointments', 'orders') NOT NULL,
    record_id INT NOT NULL,
    action ENUM('created', 'updated', 'cancelled') NOT NULL,
    old_values JSON,
    new_values JSON,
    modified_by INT NOT NULL,
    modified_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    reason TEXT,
    FOREIGN KEY (modified_by) REFERENCES patients(patient_id) ON DELETE CASCADE
);

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_appointments_patient_status ON appointments(patient_id, status);
CREATE INDEX IF NOT EXISTS idx_appointments_date ON appointments(appointment_date);
CREATE INDEX IF NOT EXISTS idx_orders_customer_status ON orders(customer_id, order_status);
CREATE INDEX IF NOT EXISTS idx_orders_date ON orders(order_date);

-- Show the updated structures
SELECT 'Updated appointments table structure:' as info;
DESCRIBE appointments;

SELECT 'Updated orders table structure:' as info;
DESCRIBE orders;

SELECT 'New modification_log table structure:' as info;
DESCRIBE modification_log;
